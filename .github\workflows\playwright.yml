name: Playwright Tests

on:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]

jobs:
  test:
    timeout-minutes: 60
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-node@v4
        with:
          node-version: lts/*
          cache: 'npm'

      # (Optional but big win) cache Playwright browser downloads between runs
      - name: Restore Playwright browsers cache
        uses: actions/cache@v4
        with:
          path: ~/.cache/ms-playwright
          key: ms-playwright-${{ hashFiles('package-lock.json') }}
          restore-keys: |
            ms-playwright-

      - name: Install dependencies
        run: npm ci

      # Install ONLY what you use (Chromium). Drop --with-deps to save ~1m.
      - name: Install Playwright (Chromium only)
        run: npx playwright install chromium

      # # ---- Probes to rule out firewall/WAF/IPv6 issues ----
      # - name: Sanity check (IPv4 headers + body snippet)
      #   run: |
      #     set -xe
      #     curl -4 -I https://qa02.wify.co.in/signin || true
      #     curl -4 -A "Mozilla/5.0" -v https://qa02.wify.co.in/signin | head -n 80 || true
      #     # Try trailing slash too (some rewrites differ)
      #     curl -4 -I https://qa02.wify.co.in/signin/ || true

      # - name: DNS + TLS check
      #   run: |
      #     set -e
      #     getent hosts qa02.wify.co.in || true
      #     nslookup qa02.wify.co.in || true
      #     echo | openssl s_client -connect qa02.wify.co.in:443 -servername qa02.wify.co.in 2>/dev/null | head -n 15 || true

      # (Optional test-only) Force IPv4 preference to detect IPv6 allowlist gaps
      # - name: Force IPv4 for this job
      #   run: |
      #     echo "precedence ::ffff:0:0/96  100" | sudo tee -a /etc/gai.conf

      # ---- Run tests (enable traces so failures are actionable) ----
      - name: Run Playwright tests
        run: npx playwright test --project=chromium --trace=on-first-retry

      # ---- Upload reports/artifacts so you can see what the runner saw ----
      - name: Upload Playwright HTML report
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: playwright-report
          path: playwright-report/
          retention-days: 30

      - name: Upload test artifacts (screenshots/videos/traces)
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: test-artifacts
          path: |
            test-artifacts
            test-results
          retention-days: 30

