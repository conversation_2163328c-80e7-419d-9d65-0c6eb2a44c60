# How to Record and Add New Tests

## Quick Start (Standard & Simple)

### 1. Create test structure (TypeScript)

```bash
npm run create-test your_feature_name
```

### 2. Record a test

```bash
npm run record
# OR
npx playwright codegen http://localhost:3000
```

### 3. Edit generated TypeScript files

The `npm run create-test` command automatically creates:

-   `tests/your_feature_name/data.ts` - TypeScript data file with interfaces
-   `tests/your_feature_name/your_feature_name.spec.ts` - TypeScript test file

### 4. Add recorded code to your test

1. <PERSON>e recorded code into the test function
2. Replace hardcoded selectors with `testData.selectors.yourSelector`
3. Add proper assertions

### 5. Run your test

```bash
npx playwright test tests/your_feature_name
```

## Example Recording Session

1. Create structure: `npm run create-test dashboard`
2. Record: `npm run record` (opens browser)
3. Interact with your app (click, type, navigate)
4. Copy generated code from Playwright Inspector
5. <PERSON>e into `tests/dashboard/dashboard.spec.ts`
6. Clean up code to use `testData` selectors
7. Run: `npx playwright test tests/dashboard`

## Tips

-   Record one user flow at a time
-   Keep recordings focused on specific features
-   Always clean up generated code to match your patterns
-   Use descriptive test names
-   Add proper assertions beyond just interactions
