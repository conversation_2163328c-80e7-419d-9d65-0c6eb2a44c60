#!/usr/bin/env node

/**
 * Simple script to create new test structure
 * Usage: npx tsx create_test.ts feature_name
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const featureName: string = process.argv[2];

if (!featureName) {
    console.log('Usage: npx tsx create_test.ts <feature_name>');
    console.log('Example: npx tsx create_test.ts dashboard');
    process.exit(1);
}

const testDir = path.join(__dirname, 'tests', featureName);

// Create test directory
if (!fs.existsSync(testDir)) {
    fs.mkdirSync(testDir, { recursive: true });
    console.log(`✓ Created directory: tests/${featureName}`);
} else {
    console.log(`Directory tests/${featureName} already exists`);
}

// Create data.ts template
const dataTemplate = `/**
 * Test data for ${featureName} functionality
 */

interface ${featureName.charAt(0).toUpperCase() + featureName.slice(1)}TestData {
    validData: Record<string, any>;
    selectors: Record<string, string>;
    pageContent: {
        title: RegExp;
    };
}

const ${featureName}TestData: ${featureName.charAt(0).toUpperCase() + featureName.slice(1)}TestData = {
    // Add your test data here
    validData: {
        // Example: name: 'Test User'
    },
    
    selectors: {
        // Add selectors found during recording
        // Example: submitButton: 'button[type="submit"]'
    },
    
    pageContent: {
        title: /Expected Title/i,
    },
};

export default ${featureName}TestData;
`;

const dataFile = path.join(testDir, 'data.ts');
if (!fs.existsSync(dataFile)) {
    fs.writeFileSync(dataFile, dataTemplate);
    console.log(`✓ Created: tests/${featureName}/data.ts`);
}

// Create spec file template
const specTemplate = `/**
 * ${featureName} functionality tests
 */

import { test, expect } from '@playwright/test';
import testData from './data';

test.describe('${
    featureName.charAt(0).toUpperCase() + featureName.slice(1)
} Tests', () => {
    test.beforeEach(async ({ page }) => {
        // Update this URL to match your feature page
        await page.goto('http://localhost:3000/${featureName}');
        await page.waitForTimeout(2000);
    });

    test('should display ${featureName} page correctly', async ({ page }) => {
        // Paste your recorded code here
        // Replace hardcoded selectors with testData.selectors
        
        // Example assertion:
        // await expect(page).toHaveTitle(testData.pageContent.title);
    });

    // Add more tests as needed
});
`;

const specFile = path.join(testDir, `${featureName}.spec.ts`);
if (!fs.existsSync(specFile)) {
    fs.writeFileSync(specFile, specTemplate);
    console.log(`✓ Created: tests/${featureName}/${featureName}.spec.ts`);
}

console.log(`\n🎉 Test structure created for "${featureName}"!`);
console.log('\nNext steps:');
console.log(
    `1. Record your test: npx playwright codegen http://localhost:3000`
);
console.log(
    `2. Edit tests/${featureName}/data.ts with your selectors and data`
);
console.log(
    `3. Edit tests/${featureName}/${featureName}.spec.ts with your recorded code`
);
console.log(`4. Run your test: npx playwright test tests/${featureName}`);
