import { test, expect } from '@playwright/test';
import SigninTestData from './data'; // Adjust the path as needed

test('Login and navigate to dashboard', async ({ page, baseURL }) => {
  const loginUrl = `${baseURL}/signin`;
  console.log('Navigating to:', loginUrl);
  await page.goto(loginUrl);

  // Print the page's HTML to debug what's actually rendered
  // const content = await page.content();
  // console.log('Page content:\n', content);

  const { emailInput, passwordInput, signInButton, dashboardTile } = SigninTestData.selectors;
  const { email, password } = SigninTestData.validData;

  await page.getByRole(emailInput.role, { name: emailInput.name }).fill(email);
  await page.getByRole(passwordInput.role, { name: passwordInput.name }).fill(password);
  await page.getByRole(signInButton.role, { name: signInButton.name }).click();

  const dashboardLocator = page.locator(dashboardTile).first();
  await expect(dashboardLocator).toBeVisible();
  await dashboardLocator.click();

  await page.waitForTimeout(4000);
  await expect(page).toHaveURL(SigninTestData.pageContent.expectedUrl);
});
