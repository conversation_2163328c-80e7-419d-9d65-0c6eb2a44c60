import { test, expect } from '@playwright/test';

test('test', async ({ page }) => {
  // Test data
  const testData = {
    startTime: '09:00AM',
    endTime: '09:30AM',
    remarks: 'test'
  };
  await page.goto('https://qa02.wify.co.in/signin');
  await page.getByRole('textbox', { name: 'Email' }).click();
  await page.getByRole('textbox', { name: 'Email' }).fill('<EMAIL>');
  await page.getByRole('textbox', { name: 'Password' }).click();
  await page.getByRole('textbox', { name: 'Password' }).fill('test');
  await page.getByRole('button', { name: 'Sign In' }).click();
  await page.getByRole('heading', { name: 'Test environment', exact: true }).locator('span').click();
  await page.getByRole('link', { name: 'e Order' }).click();
  await page.getByRole('heading', { name: 'test address 2' }).locator('div').click();
  await page.getByRole('button', { name: 'user-add Add subtask' }).click();
  await page.getByRole('menuitem', { name: ' Visit' }).locator('span').click();
  await page.locator('.ant-col.ant-col-18 > .ant-row > .ant-col.ant-form-item-control > .ant-form-item-control-input > .ant-form-item-control-input-content > .ant-picker').click();
  await page.getByText('Today').click();
  // Select start time - using more robust selector
  await page.getByRole('combobox', { name: '* Start Time' }).click();
  await page.getByRole('option', { name: '09:00AM' }).click();

  // Select end time - consistent approach
  await page.getByRole('combobox', { name: '* End Time' }).click();
  await page.getByRole('option', { name: '09:30AM' }).click();
  await page.getByRole('switch', { name: 'Show All' }).click();
  await page.locator('div:nth-child(8) > .ant-col.ant-col-18 > .ant-row > .ant-col.ant-form-item-control > .ant-form-item-control-input > .ant-form-item-control-input-content > .ant-select > .ant-select-selector > .ant-select-selection-overflow').click();
  await page.getByTitle('Bhagat.singh').locator('div').click();
  await page.getByRole('textbox', { name: '* Remarks' }).click();
  await page.getByRole('textbox', { name: '* Remarks' }).fill('test');
  await page.getByRole('button', { name: 'Create' }).click();
  await page.getByRole('cell', { name: 'Bhagat.singh' }).click();
  await page.getByRole('button', { name: 'Save' }).click();
});