import { test, expect } from '@playwright/test';

test('test', async ({ page }) => {
  await page.goto('https://qa02.wify.co.in/signin');
  await page.getByRole('textbox', { name: 'Email' }).click();
  await page.getByRole('textbox', { name: 'Email' }).fill('<EMAIL>');
  await page.getByRole('textbox', { name: 'Password' }).click();
  await page.getByRole('textbox', { name: 'Password' }).fill('test');
  await page.getByRole('button', { name: 'Sign In' }).click();
  await page.getByRole('heading', { name: 'Test environment', exact: true }).locator('span').click();
  await page.getByRole('link', { name: 'e Order' }).click();
  await page.getByRole('heading', { name: 'test address 2' }).locator('div').click();
  await page.getByRole('button', { name: 'user-add Add subtask' }).click();
  await page.getByRole('menuitem', { name: ' Visit' }).locator('span').click();
  await page.locator('.ant-col.ant-col-18 > .ant-row > .ant-col.ant-form-item-control > .ant-form-item-control-input > .ant-form-item-control-input-content > .ant-picker').click();
  await page.getByText('Today').click();
  await page.getByRole('combobox', { name: '* Start Time' }).click();
});