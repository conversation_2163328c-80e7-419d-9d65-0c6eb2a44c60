/**
 * Test data for Signin functionality
 */

interface SigninTestData {
  validData: {
    email: string;
    password: string;
  };
  selectors: {
    // Using getByRole locator strategy for accessible elements
    emailInput: { role: 'textbox'; name: string };
    passwordInput: { role: 'textbox'; name: string };
    signInButton: { role: 'button'; name: string };

    // Dashboard tile to click after successful login
    dashboardTile: string;
  };
  pageContent: {
    // Expected final URL after login and navigation
    expectedUrl: RegExp;
  };
}

const SigninTestData: SigninTestData = {
  validData: {
    // These credentials match the test case
    email: '<EMAIL>',
    password: 'test',
  },
  selectors: {
    // These match the getByRole usage in the test
    emailInput: { role: 'textbox', name: 'Email' },
    passwordInput: { role: 'textbox', name: 'Password' },
    signInButton: { role: 'button', name: 'Sign In' },

    // This selector targets the first dashboard tile
    dashboardTile:
      'div:nth-child(2) > .ant-col > .ant-list-item > .ant-list-item-meta > .ant-list-item-meta-content > .ant-list-item-meta-title > .gx-position-relative > .wy-gap-0 > div',
  },
  pageContent: {
    // RegExp used for matching final URL (e.g., /dashboard/)
    expectedUrl: /dashboard/,
  },
};

export default SigninTestData;
