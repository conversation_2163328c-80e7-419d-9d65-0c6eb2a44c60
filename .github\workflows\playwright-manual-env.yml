name: Playwright Tests (Select Env)

on:
  workflow_dispatch:
    inputs:
      env:
        description: "Target environment"
        type: choice
        required: true
        options:
          - qa01
          - qa02
          - qa03
          - qa04
          - qa05

jobs:
  test:
    timeout-minutes: 60
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-node@v4
        with:
          node-version: lts/*
          cache: "npm"

      # (Optional) cache Playwright browser downloads between runs
      - name: Restore Playwright browsers cache
        uses: actions/cache@v4
        with:
          path: ~/.cache/ms-playwright
          key: ms-playwright-${{ hashFiles('package-lock.json') }}
          restore-keys: |
            ms-playwright-

      - name: Install dependencies
        run: npm ci

      # Install ONLY what you use (Chromium)
      - name: Install Playwright (Chromium only)
        run: npx playwright install chromium

      - name: Run Playwright tests for selected env
        run: npm run test-${{ inputs.env }}

      # Upload reports/artifacts
      - name: Upload Playwright HTML report
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: playwright-report
          path: playwright-report/
          retention-days: 30

      - name: Upload test artifacts (screenshots/videos/traces)
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: test-artifacts
          path: |
            test-artifacts
            test-results
          retention-days: 30
