# TMS Integration Testing with <PERSON><PERSON>

A comprehensive integration testing suite for the TMS (Task Management System) using Playwright with TypeScript.

## 🚀 Quick Start

### Prerequisites

-   Node.js (v16 or higher)
-   TMS application running on `http://localhost:3000`

### Installation

```bash
# Install dependencies
npm install

# Install Playwright browsers
npm run install-browsers
```

### Running Tests

```bash
# Run all tests
npm test

# Run tests with UI mode (recommended for beginners)
npm run test:ui

# Run tests in headed mode (see browser)
npm run test:headed

# Run specific test
npm run test:login
```

## 📁 Project Structure

```
PlaywrightJS/
├── tests/                    # Test files organized by feature
│   └── Signin/              # Example: Sign-in tests
│       └── Signin.spec.ts   # Test specification
├── test-artifacts/          # Screenshots, videos, traces
├── test-results/           # Test execution results
├── playwright-report/      # HTML test reports
├── playwright.config.ts    # Playwright configuration
├── package.json           # Dependencies and scripts
└── readme.md              # This file
```

## 🧪 Creating New Tests

### Method 1: Automated Test Creation (Recommended)

```bash
# Create test structure for a new feature
npm run create-test your_feature_name

# This creates:
# - tests/your_feature_name/data.ts (test data)
# - tests/your_feature_name/your_feature_name.spec.ts (test file)
```

### Method 2: Record Tests Interactively

```bash
# Start recording mode
npm run record

# OR use full command
npx playwright codegen http://localhost:3000
```

**Recording Process:**

1. Browser opens with Playwright Inspector
2. Navigate and interact with your application
3. Playwright generates code automatically
4. Copy the generated code to your test file
5. Clean up and add assertions

## 📝 Writing Tests

### Basic Test Structure

```typescript
import { test, expect } from '@playwright/test';

test('feature description', async ({ page }) => {
    // Navigate to page
    await page.goto('/your-page');

    // Interact with elements
    await page.getByRole('button', { name: 'Click Me' }).click();

    // Add assertions
    await expect(page).toHaveURL(/expected-url/);
});
```

### Best Practices

-   **Use descriptive test names**: Clearly describe what the test does
-   **Add meaningful assertions**: Don't just interact, verify outcomes
-   **Use data files**: Store test data separately for maintainability
-   **Keep tests focused**: One test should verify one specific behavior
-   **Use page object patterns**: For complex applications

## 🎯 Available Test Commands

| Command                 | Description                    |
| ----------------------- | ------------------------------ |
| `npm test`              | Run all tests in headless mode |
| `npm run test:ui`       | Run tests with interactive UI  |
| `npm run test:debug`    | Run tests in debug mode        |
| `npm run test:headed`   | Run tests with visible browser |
| `npm run test:login`    | Run only login tests           |
| `npm run test:login:ui` | Run login tests with UI        |
| `npm run report`        | Open HTML test report          |
| `npm run record`        | Record new test interactions   |
| `npm run create-test`   | Create new test structure      |

## 📊 Understanding Test Results

### Test Reports

-   **HTML Report**: `npm run report` - Interactive report with screenshots
-   **Console Output**: Real-time test execution status
-   **Artifacts**: Screenshots and videos saved in `test-artifacts/`

### Test Artifacts

-   **Screenshots**: Captured on test failures
-   **Videos**: Recorded for failed tests
-   **Traces**: Detailed execution traces for debugging

## 🔧 Configuration

### Key Settings (playwright.config.ts)

-   **Base URL**: `http://localhost:3000`
-   **Timeout**: 30 seconds per test
-   **Browsers**: Chrome, Firefox, Safari
-   **Parallel Execution**: Enabled for faster runs
-   **Retry**: 2 retries on CI, 0 locally

### Environment Setup

Make sure your TMS application is running on `http://localhost:3000` before running tests.

## 🐛 Troubleshooting

### Common Issues

**Tests failing with timeout:**

-   Increase timeout in `playwright.config.ts`
-   Check if application is running on correct port

**Element not found:**

-   Use `npm run test:headed` to see what's happening
-   Update selectors if UI has changed
-   Use `npm run record` to generate new selectors

**Browser not launching:**

-   Run `npm run install-browsers`
-   Check system requirements

### Debug Mode

```bash
# Run specific test in debug mode
npx playwright test tests/Signin/Signin.spec.ts --debug

# This opens:
# - Browser with test execution
# - Playwright Inspector for step-by-step debugging
```

## 📚 Learning Resources

### For Manual Testers New to Automation

1. **Start with UI mode**: `npm run test:ui` - Visual test runner
2. **Record your first test**: `npm run record` - Learn by doing
3. **Study existing tests**: Check `tests/Signin/Signin.spec.ts`
4. **Use headed mode**: `npm run test:headed` - See tests run

### Playwright Documentation

-   [Official Playwright Docs](https://playwright.dev/)
-   [Test Generator](https://playwright.dev/docs/codegen)
-   [Selectors Guide](https://playwright.dev/docs/selectors)

## 🤝 Contributing

1. Create tests for new features using `npm run create-test`
2. Follow existing naming conventions
3. Add meaningful assertions
4. Test your changes with `npm run test:ui`
5. Ensure tests pass before committing

## 📞 Support

For questions about this testing framework:

-   Check existing test examples in `tests/` directory
-   Use `npm run test:ui` for interactive debugging
-   Refer to `record_new_test.md` for detailed recording instructions
